---
import { getLangFromUrl, getLocalizedPath, languages, type Language } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const currentPath = Astro.url.pathname;
---

<div
  x-data="languageSelector()"
  class="relative"
>
  <!-- Language Toggle Button -->
  <button
    @click="toggleDropdown()"
    class="p-2 rounded-full text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 focus:outline-hidden focus:ring-2 focus:ring-primary-500 flex items-center space-x-1"
    aria-label="Select language"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
    </svg>
    <span class="text-sm font-medium uppercase">{currentLang}</span>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
    </svg>
  </button>

  <!-- Language Dropdown -->
  <div
    x-show="isOpen"
    x-transition:enter="transition ease-out duration-200"
    x-transition:enter-start="opacity-0 scale-95"
    x-transition:enter-end="opacity-100 scale-100"
    x-transition:leave="transition ease-in duration-150"
    x-transition:leave-start="opacity-100 scale-100"
    x-transition:leave-end="opacity-0 scale-95"
    @click.away="isOpen = false"
    class="absolute top-12 right-0 w-32 py-2 bg-white dark:bg-secondary-900 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden z-50"
    x-cloak
  >
    {Object.entries(languages).map(([code, name]) => (
      <a
        href={getLocalizedPath(currentPath, code as Language)}
        class={`block px-4 py-2 text-sm hover:bg-secondary-100 dark:hover:bg-secondary-800 flex items-center justify-between ${
          currentLang === code
            ? 'text-primary-600 bg-primary-50 dark:bg-primary-900/20'
            : 'text-secondary-700 dark:text-secondary-300'
        }`}
        data-lang={code}
        data-language-selector
        @click={`trackLanguageSwitch('${currentLang}', '${code}')`}
      >
        <span>{name}</span>
        <span class="text-xs font-mono uppercase text-secondary-500">{code}</span>
      </a>
    ))}
  </div>
</div>

<script>
  function languageSelector() {
    return {
      isOpen: false,

      toggleDropdown() {
        this.isOpen = !this.isOpen;
      }
    }
  }

  // Track language switching
  function trackLanguageSwitch(fromLang, toLang) {
    if (typeof window.trackingManager !== 'undefined') {
      window.trackingManager.trackLanguageSwitch(fromLang, toLang);
    }
  }

  // Make functions globally available
  if (typeof window !== 'undefined') {
    window.languageSelector = languageSelector;
    window.trackLanguageSwitch = trackLanguageSwitch;
  }
</script>

<style>
  [x-cloak] { display: none !important; }
</style>

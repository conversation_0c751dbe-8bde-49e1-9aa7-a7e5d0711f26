---
import Layout from '../../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import RestAreaCard from '../../../components/RestAreaCard.astro';
import { getLangFromUrl, useTranslations } from '../../../i18n/utils';

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Fetch entries specifically for the current locale based on the folder structure
// Content entry IDs will be like 'en/some-area.md' or 'pl/some-area.md'
const polishRestAreas = await getCollection('rest-areas', ({ id }) => id.startsWith(`${currentLang}/`));

// Sort by title
const sortedRestAreas = polishRestAreas.sort((a, b) =>
  a.data.title.localeCompare(b.data.title)
);

// Extract unique highways for dynamic filtering from the filtered list
const uniqueHighways = [...new Set(sortedRestAreas.map(area => area.data.highway_tag))].sort();

// Get search parameters from URL
const url = new URL(Astro.request.url);
const searchParam = url.searchParams.get('search') || '';
const highwayParam = url.searchParams.get('highway') || '';

const pageTitle = t('meta.defaultTitle');
const pageDescription = `${t('meta.defaultDescription')} ${polishRestAreas.length} miejsc odpoczynku.`;
---

<script is:inline define:vars={{ uniqueHighways }}>
  // Make highway data available to Alpine.js
  window.uniqueHighways = uniqueHighways;
</script>

<Layout title={pageTitle} description={pageDescription}>
  <main>
    <div class="container-custom pt-8">
      <div class="mb-8">
        <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
          {t('nav.allRestAreas')}
        </h1>
        <p class="text-xl text-secondary-600 dark:text-secondary-300">
          Przeglądaj nasz kompletny katalog {polishRestAreas.length} miejsc odpoczynku na autostradach w Polsce
        </p>
      </div>

      <!-- Filter/Search Section -->
      <div class="mb-8">
        <div
          x-data="{
            searchTerm: new URLSearchParams(window.location.search).get('search') || '',
            selectedCountry: '',
            selectedHighway: new URLSearchParams(window.location.search).get('highway') || '',
            filteredAreas: [],
            highways: [],

            init() {
              // Get highways from the page data
              this.highways = window.uniqueHighways || [];
              this.filteredAreas = this.getAllAreas();
              // Apply initial filters if URL parameters exist
              if (this.searchTerm || this.selectedHighway) {
                this.filterAreas();
              }
            },

            getAllAreas() {
              return Array.from(document.querySelectorAll('[data-rest-area]')).map(el => ({
                element: el,
                title: el.dataset.title.toLowerCase(),
                country: el.dataset.country,
                highway: el.dataset.highway,
                location: el.dataset.location.toLowerCase(),
                address: el.dataset.address ? el.dataset.address.toLowerCase() : ''
              }));
            },

            filterAreas() {
              const areas = this.getAllAreas();

              areas.forEach(area => {
                const matchesSearch = !this.searchTerm ||
                  area.title.includes(this.searchTerm.toLowerCase()) ||
                  area.location.includes(this.searchTerm.toLowerCase()) ||
                  area.address.includes(this.searchTerm.toLowerCase()) ||
                  area.highway.toLowerCase().includes(this.searchTerm.toLowerCase());

                const matchesCountry = !this.selectedCountry || area.country === this.selectedCountry;
                const matchesHighway = !this.selectedHighway || area.highway === this.selectedHighway;

                const shouldShow = matchesSearch && matchesCountry && matchesHighway;
                area.element.style.display = shouldShow ? 'block' : 'none';
              });
            }
          }"
          class="bg-white dark:bg-secondary-900 rounded-xl p-6 shadow-lg mb-8"
        >
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                {t('common.search')}
              </label>
              <input
                type="text"
                x-model="searchTerm"
                @input="filterAreas()"
                placeholder={t('search.searchPlaceholder')}
                class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                {t('search.country')}
              </label>
              <select
                x-model="selectedCountry"
                @change="filterAreas()"
                class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
              >
                <option value="">Wszystkie kraje</option>
                <option value="PL">{t('countries.PL')}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                {t('search.highway')}
              </label>
              <select
                x-model="selectedHighway"
                @change="filterAreas()"
                class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
              >
                <option value="">Wszystkie autostrady</option>
                <template x-for="highway in highways" :key="highway">
                  <option :value="highway" x-text="highway"></option>
                </template>
              </select>
            </div>

            <div class="flex items-end">
              <button
                @click="searchTerm = ''; selectedCountry = ''; selectedHighway = ''; filterAreas();"
                class="w-full h-10 btn-outline"
              >
                Wyczyść filtry
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Rest Areas Grid -->
      <section class="mb-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedRestAreas.map((restArea) => (
            <div
              data-rest-area
              data-title={restArea.data.title}
              data-country={restArea.data.country_code}
              data-highway={restArea.data.highway_tag}
              data-location={restArea.data.location_path}
              data-address={restArea.data.address_line}
            >
              <RestAreaCard restArea={restArea} />
            </div>
          ))}
        </div>
      </section>

      <!-- No Results Message -->
      <div
        x-show="Array.from(document.querySelectorAll('[data-rest-area]')).every(el => el.style.display === 'none')"
        class="text-center py-12 mb-16"
        style="display: none;"
      >
        <div class="max-w-md mx-auto">
          <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
            {t('common.noResults')}
          </h2>
          <p class="text-secondary-600 dark:text-secondary-400 mb-6">
            Spróbuj dostosować kryteria wyszukiwania lub wyczyść filtry.
          </p>
          <button
            @click="searchTerm = ''; selectedCountry = ''; selectedHighway = ''; filterAreas();"
            class="btn-primary"
          >
            Wyczyść wszystkie filtry
          </button>
        </div>
      </div>
    </div>
  </main>
</Layout>
